import base64
import json
import re
import time
import traceback
import xml.etree.ElementTree as ET
from typing import Optional

import httpx
from pydantic import BaseModel, Field
from api.models.xml_infer import parsePageResponse
from api.utils.svrkit.ttlkv import async_batch_get_sum_ttlkv
from api.utils.wecube.infer_log import log
from api.utils.xml_ui_former.convert_wechat_minihtml_to_xml import convert as wxconvert
from api.utils.xml_ui_former.simplify_xml import (
    add_attributes_to_tabbar_items,
    parameterize_actions,
    process_accessibility_tree,
)

text_pattern = re.compile(r"(text:\s*)([^;]*)(;)")
idx_pattern = re.compile(r"^\s*\[\s*(\d+)\s*\]")
resource_id_pattern = re.compile(r"resource_id: .*?; ")


class ExtraConfig(BaseModel):
    """XML推理服务的额外配置"""

    disable_omniparser: bool = Field(
        default=False,
        description="是否禁用 omniparser 服务，禁用后将不会调用 omniparser 进行文本识别"
    )

    disable_xpath_summary: bool = Field(
        default=False,
        description="是否禁用 xpath 摘要功能，禁用后将不会从 KV 存储中获取 xpath 对应的摘要信息"
    )

    xpath_summary_prefix: Optional[str] = Field(
        default=None,
        description="xpath 摘要的前缀，用于在 KV 存储中查找对应的摘要信息"
    )


class parse_page_service:
    def __init__(
        self,
        app_id: str,
        parser_url: str,
        screen_url: str,
        dom_xml: str,
        elements_rects: list,
        native_elements_rects: list,
        trace_id: str = "default",
        extra_config: Optional[ExtraConfig] = None,
    ) -> None:
        self.app_id = app_id
        self.parser_url = parser_url
        self.screen_url = screen_url
        self.dom_xml = dom_xml
        self.elements_rects = elements_rects
        self.native_elements_rects = native_elements_rects
        self.extra_config = extra_config or ExtraConfig()
        try:
            self.trace_id = trace_id.split("-")[1]
        except Exception:
            self.trace_id = trace_id

    async def call_omniparser(self, rects: list[dict]) -> dict:
        """
        调用 omniparser 服务接口

        :param url: 服务接口的 url
        :param file: 本地文件的路径
        :param rects: 元素的框矩形信息列表
        :return: omniparser 服务接口返回处理后的结果
        """
        if self.screen_url == "":
            return {}
        # get image64 from image_url
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(self.screen_url)
                response.raise_for_status()
                image_base64 = base64.b64encode(response.content).decode("utf-8")
            except Exception as e:
                log(
                    f"Failed to get image from URL: {self.screen_url}",
                    trace_id=self.trace_id,
                    extra_info={"exception": str(e)},
                )
                return {}

        if not image_base64 or not rects:
            return {}
        # 构造请求体
        payload = {
            "image_base64": image_base64,
            "rects": rects,
            "save_outputs": False,
            "max_area_ratio": 0.5,
        }
        res = {}
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url=self.parser_url,
                    content=json.dumps(payload),
                    headers={"Content-Type": "application/json"},
                )
                # 检查响应状态
                response.raise_for_status()

                # 处理响应结果
                response_data = response.json()
                response_result = response_data.get("result", [])
                log(
                    "get omniparser response result",
                    trace_id=self.trace_id,
                    extra_info={
                        "customized_data_info": {"response_result": response_result}
                    },
                )

                for d in response_data["result"]:
                    xpath = "/" + d["xpath"]
                    ocr = d["paddleocr"]
                    caption = d["florence2"]
                    res[xpath] = ocr if ocr else caption

        except Exception as e:  # pylint: disable=W0718
            log(
                "call_omniparser failed",
                trace_id=self.trace_id,
                extra_info={
                    "customized_data_info": {
                        "api_url": self.parser_url,
                        "rects": rects,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return res

    @staticmethod
    def deduplicate_between_dicts(data1: dict, data2: dict) -> dict:
        """
        去重
        """
        return {
            key1: value1
            for key1, value1 in data1.items()
            if not any(
                key2.startswith(key1) and value2 and value2 in value1
                for key2, value2 in data2.items()
            )
        }

    @staticmethod
    def clean_screen(screen: str) -> str:
        screen = resource_id_pattern.sub("", screen)
        screen = screen.replace("text: ", "")
        screen = screen.replace("(; ", "(")
        screen = screen.replace("actions: ", "")
        screen = screen.replace(" )", ")")
        screen = screen.replace("wx-", "")
        return screen

    def add_text(
        self,
        screen: str,
        idx_to_xpath: dict[int, str],
        omni_res: dict,
        xpath_summary_map: dict,
    ) -> tuple[str, str]:
        outputs = []
        log_outputs = []
        lines = screen.split("\n")[::-1]
        # 收集所有 xpath 处理信息
        xpath_process_info = []
        summary_map = {}
        for line in lines:
            idx_match = idx_pattern.search(line)
            if not idx_match:
                outputs.append(line)
                continue
            idx = int(idx_match.group(1))
            xpath = idx_to_xpath.get(idx)
            if not xpath:
                continue
            # 收集处理信息
            summary_text = xpath_summary_map.get(xpath, "")
            for k, v in summary_map.items():
                if k.startswith(xpath) and v == summary_text:
                    summary_text = ""
                    break
            summary_map[xpath] = summary_text
            omni_text = omni_res.get(xpath, "")

            text_match = text_pattern.search(line)
            text = text_match.group(2) if text_match else ""
            original_text = text
            if set(omni_text) & set(text):
                omni_text = ""
            if summary_text in text:
                summary_text = ""

            text_list = []
            log_text_list = []
            for content, tag in zip(
                [text, omni_text, summary_text], ["[text]", "[omni]", "[summary]"]
            ):
                if content:
                    text_list.append(content)
                    log_text_list.append(content + tag)
            merge_text = "；".join(text_list)
            line = re.sub(text_pattern, r"\g<1>" + f"{merge_text}" + r"\g<3>", line)
            outputs.append(line)

            log_merge_text = "；".join(log_text_list)
            log_line = re.sub(
                text_pattern, r"\g<1>" + f"{log_merge_text}" + r"\g<3>", line
            )
            log_outputs.append(log_line)

            # 添加到收集的信息中
            xpath_process_info.append(
                {
                    "idx": idx,
                    "xpath": xpath,
                    "original_text": original_text,
                    "redis_result": summary_text,
                    "omni_result": omni_text,
                    "final_text": merge_text,
                }
            )

        result = self.clean_screen("\n".join(outputs[::-1]))
        log_result = self.clean_screen("\n".join(log_outputs[::-1]))
        # 记录所有 xpath 处理结果
        log(
            "add_text success",
            trace_id=self.trace_id,
            extra_info={
                "customized_data_info": {
                    "xpath_process_info": xpath_process_info,
                    "result": result,
                    "log_result": log_result,
                }
            },
        )
        return result, log_result

    async def run(self) -> parsePageResponse:
        try:
            start_time = time.time()
            rects = (self.elements_rects) + (self.native_elements_rects)
            
            # 获取 dom_xml 数据
            xml_data: str = self.dom_xml
            xml_data = (
                xml_data.replace("<page>", "")
                .replace("</page>", "")
                .replace("<iframe>", "")
                .replace("</iframe>", "")
            )
            xml_data = add_attributes_to_tabbar_items(xml_data)
            # 将xcx html 转换为 xml
            xml = wxconvert(xml_data)
            root = ET.fromstring(xml)
            _, elements = process_accessibility_tree(root)
            # 将 rects 转换为 xpath 到 rect 的映射, 如果 xpath 为空, 则使用空字符串作为 key
            # 其中 rect 中的 xpath 是没有"/"前缀的，需要添加
            rects_map = {
                "/" + rect["xpath"] if rect.get("xpath") else "": rect.get("rect", {})
                for rect in rects
            }
            screen, idx_to_xpath, xpath_to_text = parameterize_actions(elements, root)
            # 将 idx_to_xpath 转换为 idx_to_rect 的映射
            idx_to_rect = {
                idx: rects_map.get(xpath, {}) for idx, xpath in idx_to_xpath.items()
            }
            original_screen = screen

            # 调用 omniparser 服务接口
            if self.extra_config.get("disable_omniparser"):
                omniparser_result = {}
            else:
                omniparser_result = await self.call_omniparser(
                    rects=rects,
                )
                omniparser_result = self.deduplicate_between_dicts(
                    omniparser_result, xpath_to_text
                )
            
            # 获取 xpath 对应的 KV
            if self.extra_config.get("disable_xpath_summary"):
                xpath_summary_map = {}
            else:
                prefix = self.extra_config.get("xpath_summary_prefix")
                xpath_summary_map = await async_batch_get_sum_ttlkv(
                    app_id=self.app_id, xpaths=list(xpath_to_text.keys()), prefix=prefix
                )
                # KV hit rate
                if len(xpath_summary_map) > 0:
                    log(
                        "KV hit rate",
                        trace_id=self.trace_id,
                        extra_info={
                            "customized_data_info": {
                                "hit_rate": len(
                                    [
                                        xpath
                                        for xpath in xpath_summary_map
                                        if xpath_summary_map[xpath]
                                    ]
                                )
                                / len(xpath_summary_map)
                            }
                        },
                    )
                else:
                    log(
                        "KV hit rate",
                        trace_id=self.trace_id,
                        extra_info={"customized_data_info": {"hit_rate": 0}},
                    )
            screen, log_screen = self.add_text(
                screen, idx_to_xpath, omniparser_result, xpath_summary_map
            )
            xpath_to_idx = {path: idx for idx, path in idx_to_xpath.items()}

            result = parsePageResponse(
                xml_data=xml_data,
                idx_to_xpath=idx_to_xpath,
                xpath_to_idx=xpath_to_idx,
                screen=screen,
                original_screen=original_screen,
                log_screen=log_screen,
                idx_to_rect=idx_to_rect,
            )
            end_time = time.time()
            excute_time = end_time - start_time
            log(
                "parse_page success",
                trace_id=self.trace_id,
                extra_info={
                    "customized_data_info": {
                        "excute_time": excute_time,
                        "result": result.model_dump(),
                    }
                },
            )
            return result
        except Exception as e:  # pylint: disable=W0718
            log(
                "parse_page failed",
                trace_id=self.trace_id,
                extra_info={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            return parsePageResponse(
                xml_data="",
                idx_to_xpath={},
                xpath_to_idx={},
                screen="",
                original_screen="",
                log_screen="",
                idx_to_rect={},
            )
