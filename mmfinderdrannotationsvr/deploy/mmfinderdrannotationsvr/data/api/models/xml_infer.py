from typing import Optional
from pydantic import BaseModel


class parsePageResponse(BaseModel):
    # XMLInfoDTO
    xml_data: str
    idx_to_xpath: dict[int, str]
    xpath_to_idx: dict[str, int]
    screen: str
    original_screen: str
    log_screen: str
    idx_to_rect: dict[int, dict]


class parsePageRequest(BaseModel):
    app_id: str
    parser_url: str
    screen_url: str
    dom_xml: str
    elements_rects: list
    native_elements_rects: list
    extra_config: dict = {}
