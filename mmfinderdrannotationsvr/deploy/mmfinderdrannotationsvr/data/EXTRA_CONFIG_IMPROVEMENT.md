# ExtraConfig Pydantic Model 改进

## 概述

将 `parse_page_service` 类中的 `extra_config` 参数从 `dict` 类型改为 Pydantic `BaseModel`，提供更好的类型安全和数据验证。

## 改进内容

### 1. 新增 ExtraConfig 模型

```python
class ExtraConfig(BaseModel):
    """XML推理服务的额外配置"""
    
    disable_omniparser: bool = Field(
        default=False,
        description="是否禁用 omniparser 服务，禁用后将不会调用 omniparser 进行文本识别"
    )
    
    disable_xpath_summary: bool = Field(
        default=False,
        description="是否禁用 xpath 摘要功能，禁用后将不会从 KV 存储中获取 xpath 对应的摘要信息"
    )
    
    xpath_summary_prefix: Optional[str] = Field(
        default=None,
        description="xpath 摘要的前缀，用于在 KV 存储中查找对应的摘要信息"
    )
```

### 2. 更新构造函数

**之前:**
```python
def __init__(
    self,
    # ... 其他参数
    extra_config: dict = {},
) -> None:
    self.extra_config = extra_config
```

**之后:**
```python
def __init__(
    self,
    # ... 其他参数
    extra_config: Optional[ExtraConfig] = None,
) -> None:
    self.extra_config = extra_config or ExtraConfig()
```

### 3. 更新使用方式

**之前:**
```python
if self.extra_config.get("disable_omniparser"):
    # ...
prefix = self.extra_config.get("xpath_summary_prefix")
```

**之后:**
```python
if self.extra_config.disable_omniparser:
    # ...
prefix = self.extra_config.xpath_summary_prefix
```

## 改进的好处

### 1. 类型安全
- **明确的类型定义**: 每个配置项都有明确的类型（bool, Optional[str]）
- **IDE 支持**: 更好的代码补全和类型提示
- **编译时检查**: 可以在开发阶段发现类型错误

### 2. 数据验证
- **自动验证**: Pydantic 会自动验证输入数据的类型和格式
- **错误提示**: 提供清晰的验证错误信息
- **类型转换**: 自动进行合理的类型转换

### 3. 文档化
- **字段描述**: 每个字段都有清晰的中文描述
- **默认值**: 明确显示每个字段的默认值
- **自动生成文档**: 可以自动生成 API 文档

### 4. 向后兼容
- **字典支持**: 仍然可以从字典创建配置 `ExtraConfig(**config_dict)`
- **默认值**: 不传递 extra_config 时使用默认配置
- **渐进式迁移**: 可以逐步迁移现有代码

## 使用示例

### 基本使用
```python
# 使用默认配置
service = parse_page_service(
    app_id="test",
    parser_url="http://example.com",
    screen_url="http://example.com/screen.jpg",
    dom_xml="<div>test</div>",
    elements_rects=[],
    native_elements_rects=[],
)

# 自定义配置
config = ExtraConfig(
    disable_omniparser=True,
    xpath_summary_prefix="custom_prefix"
)
service = parse_page_service(
    # ... 其他参数
    extra_config=config
)
```

### 从字典创建（向后兼容）
```python
config_dict = {
    "disable_omniparser": False,
    "disable_xpath_summary": True,
    "xpath_summary_prefix": "test_prefix"
}
config = ExtraConfig(**config_dict)
```

### 数据验证
```python
try:
    # 这会引发验证错误
    invalid_config = ExtraConfig(disable_omniparser="not_a_boolean")
except ValidationError as e:
    print(f"配置验证失败: {e}")
```

## 迁移指南

1. **新代码**: 直接使用 `ExtraConfig` 模型
2. **现有代码**: 可以继续使用字典，通过 `ExtraConfig(**dict)` 转换
3. **逐步迁移**: 在调用处逐步替换为 Pydantic 模型

## 注意事项

- 确保安装了 `pydantic` 依赖
- 现有的字典配置需要通过 `ExtraConfig(**dict)` 转换
- 新增配置项时需要更新 `ExtraConfig` 模型定义
