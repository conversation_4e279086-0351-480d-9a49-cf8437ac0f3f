"""
示例：如何使用新的 ExtraConfig Pydantic model

这个文件展示了如何使用新的 ExtraConfig 模型来替代原来的 dict 配置
"""

from api.services.xml_infer import parse_page_service, ExtraConfig


def example_usage():
    """展示如何使用 ExtraConfig"""
    
    # 方式1: 使用默认配置
    service1 = parse_page_service(
        app_id="test_app",
        parser_url="http://example.com/parser",
        screen_url="http://example.com/screen.jpg",
        dom_xml="<div>test</div>",
        elements_rects=[],
        native_elements_rects=[],
        trace_id="test-123",
        # extra_config 参数可以省略，会使用默认值
    )
    
    # 方式2: 使用自定义配置
    config = ExtraConfig(
        disable_omniparser=True,  # 禁用 omniparser
        disable_xpath_summary=False,  # 启用 xpath 摘要
        xpath_summary_prefix="test_prefix"  # 设置摘要前缀
    )
    
    service2 = parse_page_service(
        app_id="test_app",
        parser_url="http://example.com/parser",
        screen_url="http://example.com/screen.jpg",
        dom_xml="<div>test</div>",
        elements_rects=[],
        native_elements_rects=[],
        trace_id="test-123",
        extra_config=config
    )
    
    # 方式3: 从字典创建配置（向后兼容）
    config_dict = {
        "disable_omniparser": False,
        "disable_xpath_summary": True,
        "xpath_summary_prefix": "another_prefix"
    }
    
    config3 = ExtraConfig(**config_dict)
    
    service3 = parse_page_service(
        app_id="test_app",
        parser_url="http://example.com/parser",
        screen_url="http://example.com/screen.jpg",
        dom_xml="<div>test</div>",
        elements_rects=[],
        native_elements_rects=[],
        trace_id="test-123",
        extra_config=config3
    )
    
    # 方式4: 验证配置
    try:
        # 这会自动验证数据类型
        invalid_config = ExtraConfig(
            disable_omniparser="not_a_boolean",  # 这会引发验证错误
        )
    except Exception as e:
        print(f"配置验证失败: {e}")
    
    # 访问配置属性
    print(f"Service1 - disable_omniparser: {service1.extra_config.disable_omniparser}")
    print(f"Service2 - disable_omniparser: {service2.extra_config.disable_omniparser}")
    print(f"Service3 - xpath_summary_prefix: {service3.extra_config.xpath_summary_prefix}")


if __name__ == "__main__":
    example_usage()
